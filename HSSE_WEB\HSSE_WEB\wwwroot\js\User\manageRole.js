﻿$(document).ready(function () {
    attachRequiredValidation('#userRoleId', 'Role name is required');
    // Initial check on page load
    checkRequiredFields();

    $('#roleForm').on('submit', function (e) {
        e.preventDefault();

        // Validate required fields using the new validation function
        if (window.validateAllRequiredFields && !window.validateAllRequiredFields($(this))) {
            // Validation failed - errors are already shown on form controls
            return;
        }

        var formData = new FormData(this);
        formData.set('IsActive', $('#SeededRole').is(':checked')); // force update
        $.ajax({
            type: 'POST',
            url: createRole,
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                if (response.success) {
                    showSuccessToast(response.message);
                    $('#roleForm')[0].reset();
                    $('#RoleId').val(0);

                    loadRolesTable();
                } else {
                    showDangerToast(response.message);
                }
            },
            error: function (xhr) {
                const errorMessage = xhr.responseJSON?.message || Messages.GeneralError;
                showDangerToast(errorMessage);
            }
        });
    });

    loadRolesTable();
});

function loadRolesTable() {
    const table = $('#order-listing');

    // Destroy DataTable if already initialized
    if ($.fn.DataTable.isDataTable(table)) {
        table.DataTable().clear().destroy();
    }

    // Clear table body before refill
    const tbody = table.find('tbody');
    tbody.empty();
    $.ajax({
        url: getAllRoles, // ✅ Make sure this returns JSON list of roles
        method: 'GET',
        success: function (data) {
            let tbody = $('#rolesTableBody');
            tbody.empty();

            if (data && data.length > 0) {
                data.forEach((role, index) => {
                    const statusBadge = role.isActive
                        ? '<label class="badge badge-success">Active</label>'
                        : '<label class="badge badge-danger">Inactive</label>';

                    const seededBadge = role.seededRole
                        ? '<span class="badge badge-success">Yes</span>'
                        : '<span class="badge badge-secondary">No</span>';

                    // Tooltip texts
                    const editTooltip = role.seededRole
                        ? "This is a system (seeded) role and cannot be edited."
                        : "Edit this role";

                    const statusTooltip = role.seededRole
                        ? "This is a system (seeded) role and its status cannot be changed."
                        : (role.isActive ? "Click to deactivate this role" : "Click to activate this role");

                    // Conditionally set the edit and status buttons
                    const editButton = `
        <button type="button"
                class="btn btn-sm ${role.seededRole ? 'btn-outline-secondary' : 'btn-outline-primary'}"
                title="${editTooltip}"
                data-toggle="tooltip"
                data-custom-class="${role.seededRole ? 'tooltip-warning' : 'tooltip-primary'}"
                ${role.seededRole ? 'disabled' : `onclick="editRole(${role.roleId})"`}>
            <i class="mdi mdi-pencil"></i>
        </button>`;

                    const actionButton = `
        <button type="button"
                class="btn btn-sm ${role.seededRole ? 'btn-outline-secondary' : (role.isActive ? 'btn-outline-warning' : 'btn-outline-success')}"
                title="${statusTooltip}"
                data-toggle="tooltip"
                data-custom-class="${role.seededRole ? 'tooltip-secondary' : (role.isActive ? 'tooltip-warning' : 'tooltip-success')}"
                ${role.seededRole ? 'disabled' : `onclick="toggleRoleStatus(${role.roleId})"`}>
            <i class="mdi ${role.seededRole ? 'mdi-lock-outline' : (role.isActive ? 'mdi-close-circle-outline' : 'mdi-check-circle-outline')}"></i>
        </button>`;


                    const row = `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${role.roleName}</td>
                            <td>${seededBadge}</td>
                            <td>${statusBadge}</td>
                            <td>
                         ${editButton}
                                ${actionButton}
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
              
            } else {
                tbody.append('<tr><td colspan="6" class="text-center">No roles found.</td></tr>');
            }
            initializeDataTable('#order-listing');
        },
        error: function () {
            alert('Failed to load roles.');
        }
    });
}

function toggleRoleStatus(roleId) {
    $.ajax({
        type: 'POST',
        url: toggleRoleActivation,  // Endpoint for toggling role status
        data: { roleId: roleId },
        success: function (response) {
            if (response.success) {
                if (response.data == true) {
                    showSuccessToast(response.message);
                }
                else {
                    showWarningToast(response.message);
                }
                loadRolesTable(); // Reload the roles table after successful status update
            } else {
                showWarningToast(response.message);
            }
        },
        error: function (xhr) {
            const errorMessage = xhr.responseJSON?.message || Messages.GeneralError;
            showDangerToast(errorMessage);
        }
    });
}


function editRole(roleId) {
    $.ajax({
        url: getAllUserRoleById,
        type: 'GET',
        data: { roleId: roleId },
        success: function (role) {
            if (role) {
                $('#editRoleId').val(role.roleId);
                $('#editRoleName').val(role.roleName);
                //$('#editIsSeeded').prop('checked', role.seededRole);
                $('#editRoleModal').modal('show');
            } else {
                showSwal('error-message', 'Failed to fetch role details.');
            }
        },
        error: function () {
            showSwal('error-message', 'Server error while fetching role.');
        }
    });
}

function updateRole() {
    var roleId = $('#editRoleId').val();
    var roleName = $('#editRoleName').val();
    //var isActive = $('#editIsSeeded').is(':checked');

    $.ajax({
        url: createRole,
        type: 'POST',
        data: {
            roleId: roleId,
            roleName: roleName,
            //seededRole: isActive
        },
        success: function (res) {
            if (res.success) {
                $('#editRoleModal').modal('hide');
                showSuccessToast(Messages.UpdateRoleSuccess);
                loadRolesTable();
            } else {
                showWarningToast(res.message);
            }
        },
        error: function (xhr) {
            const errorMessage = xhr.responseJSON?.message || Messages.GeneralError;
            showDangerToast(errorMessage);        }
    });
}

