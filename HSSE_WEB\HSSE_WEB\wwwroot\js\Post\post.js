$(document).ready(function () {
    attachRequiredValidation('#Title', 'Title is required');
    attachRequiredValidation('#PostType', 'Post Type is required');
    attachRequiredValidation('#FacilityId', 'Facility is required');
    attachRequiredValidation('#PostFile', 'Image is required');

    // Initial check on page load
    checkRequiredFields();
    $('#postForm').on('submit', async function (e) {
        e.preventDefault();

        // Validate required fields using the new validation function
        if (window.validateAllRequiredFields && !window.validateAllRequiredFields($(this))) {
            // Validation failed - errors are already shown on form controls
            return;
        }
        var description = tinymce.get('tinyMceExample') ? tinymce.get('tinyMceExample').getContent() : $('#tinyMceExample').val();
        const fileInput = $('#ThumbnailFile')[0];
        let base64File = null;
        let fileName = null;
        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];
            base64File = await toBase64(file);
            fileName = file.name;
        }



        var postData = {
            postId: 0,
            userId: 0, // set in controller
            facilityId: $('#FacilityId').val() ? parseInt($('#FacilityId').val()) : null,
            title: $('#Title').val(),
            description: description,
            postType: $('#PostType').val(),
            location: $('#Location').val(),
            taggedCategoryId: $('#TaggedCategoryId').val() ? parseInt($('#TaggedCategoryId').val()) : null,
            requiresFollowup: $('#RequiresFollowup').is(':checked') ? 'Yes' : 'No',
            status: 0,
            createdAt: new Date().toISOString(),
            imageBase64: base64File,
            fileName: fileName
        };
        $.ajax({
            url: createPost,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(postData),
            success: function (response) {
                showSuccessToast(response.message || 'Post created successfully.');
                $('#postForm')[0].reset();
                $('#ThumbnailFile').val('');
                $('#ThumbnailFileName').val('');
                $('#ThumbnailPreview').hide();
                $('#ThumbnailPreviewContainer').addClass('d-none');
                $('#removeThumbnailBtn').addClass('d-none');
                if (tinymce.get('tinyMceExample')) tinymce.get('tinyMceExample').setContent('');
            },
            error: function (xhr) {
                let msg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : 'Error creating post.';
                showDangerToast(msg);
            }
        });
    });
});
$('#ThumbnailBtn').click(function () {
    $('#ThumbnailFile').click();
});

// File selection
$('#ThumbnailFile').on('change', function (e) {
    const file = e.target.files[0];
    if (file) {
        $('#ThumbnailFileName').val(file.name);

        const reader = new FileReader();
        reader.onload = function (e) {
            $('#ThumbnailPreview').attr('src', e.target.result);
            $('#ThumbnailPreviewContainer').removeClass('d-none');
            $('#removeThumbnailBtn').removeClass('d-none');
        };
        reader.readAsDataURL(file);
    }
});

// Remove selected image
$('#removeThumbnailBtn').click(function () {
    $('#AnnouncementFile').val('');
    $('#ThumbnailFileName').val('');
    $('#ThumbnailPreview').attr('src', '');
    $('#ThumbnailPreviewContainer').addClass('d-none');
    $(this).addClass('d-none');
});

// Helper function to convert file to Base64
function toBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}