﻿(function ($) {
    'use strict';

    // Attach required field validation
    window.attachRequiredValidation = function (selector, message, type = "") {
        $(selector).attr('data-required', 'true');
        if (type) {
            $(selector).attr('data-type', type);
        }

        function isValidEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        }

        function getPasswordErrorMessage(password) {
            if (password.length < 8 || password.length > 12) {
                return "Password must be between 8 and 12 characters.";
            }
            if (!/[a-zA-Z]/.test(password)) {
                return "Password must include at least one letter.";
            }
            if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {
                return "Password must include at least one special character.";
            }
            if (!/[0-9]/.test(password)) {
                return "Password must include numbers.";
            }
            return "";
        }
        //if (selector === '#tinyMceExample' && tinymce.get('tinyMceExample')) {
        //    tinymce.get('tinyMceExample').on('blur', function () {
        //        const content = tinymce.get('tinyMceExample').getContent({ format: 'text' }).trim();
        //        const $textarea = $('#tinyMceExample');

        //        $textarea.removeClass('is-invalid');
        //        $textarea.nextAll('.invalid-feedback').remove();

        //        if (!content) {
        //            $textarea.addClass('is-invalid');
        //            $textarea.after(`<div class="invalid-feedback">${message}</div>`);
        //        }

        //        window.checkRequiredFields();
        //    });

        //    // Remove existing input listener if any (TinyMCE won't use it)
        //    return;
        //}
        // On blur validation
        $(document).on('blur', selector, function () {
            const $input = $(this);
            const value = $input.val().trim();
            $input.removeClass('is-invalid');
            $input.nextAll('.invalid-feedback').remove();

            if (value === '') {
                $input.addClass('is-invalid');
                $input.after(`<div class="invalid-feedback">${message}</div>`);
            } else if (type === 'email' && !isValidEmail(value)) {
                $input.addClass('is-invalid');
                $input.after(`<div class="invalid-feedback">Please enter a valid email</div>`);
            } else if (type === 'password') {
                const passwordError = getPasswordErrorMessage(value);
                if (passwordError) {
                    $input.addClass('is-invalid');
                    $input.after(`<div class="invalid-feedback">${passwordError}</div>`);
                }
            }

            window.checkRequiredFields();
        });

        // On input cleanup
        $(document).on('input', selector, function () {
            const $input = $(this);
            const value = $input.val().trim();
            if (value !== '') {
                $input.removeClass('is-invalid');
                $input.next('.invalid-feedback').remove();
            }
            window.checkRequiredFields();
        });
    };

    // Check if all required fields are filled
    window.checkRequiredFields = function () {
        let allFilled = true;
        const form = $('form'); // Adjust if needed

        form.find('[data-required="true"]').each(function () {
            const $field = $(this);
            const tag = $field.prop('tagName').toLowerCase();
            const type = $field.attr('type') || '';
            const value = $field.val();

            if (tag === 'select') {
                if (!value || (Array.isArray(value) && value.length === 0)) {
                    allFilled = false;
                    return false;
                }
            } else if (type === 'checkbox' || type === 'radio') {
                if (!$field.is(':checked')) {
                    allFilled = false;
                    return false;
                }
            } else {
                if (!value || value.trim() === '') {
                    allFilled = false;
                    return false;
                }
            }

        });

        // Submit buttons are always enabled - validation feedback will be shown on form controls
        // form.find('button[type="submit"]').prop('disabled', !allFilled);
        return allFilled;
    };
    window.validateTinyMCE = function (editor) {
        const content = editor.getContent({ format: 'text' }).trim();
        const $textarea = $(editor.getElement());
        $textarea.removeClass('is-invalid');
        $textarea.nextAll('.invalid-feedback').remove();

        if (content === '') {
            $textarea.addClass('is-invalid');
            $textarea.after(`<div class="invalid-feedback">Description is required.</div>`);
        }

        window.checkRequiredFields(); // Update submit button state
    };

    // Validate all required fields and show validation feedback on submit
    window.validateAllRequiredFields = function (formSelector = 'form') {
        let allValid = true;
        const form = $(formSelector);

        form.find('[data-required="true"]').each(function () {
            const $field = $(this);
            const tag = $field.prop('tagName').toLowerCase();
            const type = $field.attr('type') || '';
            const dataType = $field.attr('data-type') || '';
            const value = $field.val();

            // Clear previous validation state
            $field.removeClass('is-invalid');
            $field.nextAll('.invalid-feedback').remove();

            let isFieldValid = true;
            let errorMessage = '';

            // Check if field is empty
            if (tag === 'select') {
                if (!value || (Array.isArray(value) && value.length === 0)) {
                    isFieldValid = false;
                    errorMessage = 'This field is required';
                }
            } else if (type === 'checkbox' || type === 'radio') {
                if (!$field.is(':checked')) {
                    isFieldValid = false;
                    errorMessage = 'This field is required';
                }
            } else {
                if (!value || value.trim() === '') {
                    isFieldValid = false;
                    errorMessage = 'This field is required';
                } else {
                    // Additional validation for specific types
                    if (dataType === 'email') {
                        function isValidEmail(email) {
                            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
                        }
                        if (!isValidEmail(value.trim())) {
                            isFieldValid = false;
                            errorMessage = 'Please enter a valid email';
                        }
                    } else if (dataType === 'password') {
                        function getPasswordErrorMessage(password) {
                            if (password.length < 8 || password.length > 12) {
                                return "Password must be between 8 and 12 characters.";
                            }
                            if (!/[a-zA-Z]/.test(password)) {
                                return "Password must include at least one letter.";
                            }
                            if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {
                                return "Password must include at least one special character.";
                            }
                            if (!/[0-9]/.test(password)) {
                                return "Password must include numbers.";
                            }
                            return "";
                        }
                        const passwordError = getPasswordErrorMessage(value.trim());
                        if (passwordError) {
                            isFieldValid = false;
                            errorMessage = passwordError;
                        }
                    }
                }
            }

            if (!isFieldValid) {
                $field.addClass('is-invalid');
                $field.after(`<div class="invalid-feedback">${errorMessage}</div>`);
                allValid = false;
            }
        });

        return allValid;
    };

    // Global form submission handler for validation
    $(document).ready(function() {
        // Handle form submissions globally
        $(document).on('submit', 'form', function(e) {
            const form = $(this);

            // Skip validation for forms that explicitly opt out
            if (form.hasClass('no-validation') || form.attr('data-no-validation') === 'true') {
                return true;
            }

            // Check if form has required fields
            if (form.find('[data-required="true"]').length > 0) {
                // Validate all required fields
                const isValid = window.validateAllRequiredFields(form);

                if (!isValid) {
                    e.preventDefault();

                    // Scroll to first invalid field
                    const firstInvalidField = form.find('.is-invalid').first();
                    if (firstInvalidField.length > 0) {
                        firstInvalidField[0].scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });
                        firstInvalidField.focus();
                    }

                    return false;
                }
            }
        });

        // Handle submit button clicks to ensure validation runs
        $(document).on('click', 'button[type="submit"], input[type="submit"]', function(e) {
            const button = $(this);
            const form = button.closest('form');

            // Skip validation for forms that explicitly opt out
            if (form.hasClass('no-validation') || form.attr('data-no-validation') === 'true') {
                return true;
            }

            // Check if form has required fields
            if (form.find('[data-required="true"]').length > 0) {
                // Validate all required fields
                const isValid = window.validateAllRequiredFields(form);

                if (!isValid) {
                    e.preventDefault();

                    // Scroll to first invalid field
                    const firstInvalidField = form.find('.is-invalid').first();
                    if (firstInvalidField.length > 0) {
                        firstInvalidField[0].scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });
                        firstInvalidField.focus();
                    }

                    return false;
                }
            }
        });
    });

})(jQuery);
