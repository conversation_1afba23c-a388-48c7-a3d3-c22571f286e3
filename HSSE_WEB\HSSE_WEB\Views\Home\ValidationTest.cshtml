@{
    ViewData["Title"] = "Validation Test";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12 grid-margin">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">Form Validation Test</h4>
                    <p class="card-description">Test the new validation system where submit buttons are always enabled and validation feedback appears on form controls.</p>
                    
                    <form id="testForm" class="form-sample">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">First Name<span class="text-danger">*</span></label>
                                    <div class="col-sm-9">
                                        <input type="text" id="firstName" name="FirstName" class="form-control" data-required="true" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">Last Name<span class="text-danger">*</span></label>
                                    <div class="col-sm-9">
                                        <input type="text" id="lastName" name="LastName" class="form-control" data-required="true" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">Email<span class="text-danger">*</span></label>
                                    <div class="col-sm-9">
                                        <input type="email" id="email" name="Email" class="form-control" data-required="true" data-type="email" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">Password<span class="text-danger">*</span></label>
                                    <div class="col-sm-9">
                                        <input type="password" id="password" name="Password" class="form-control" data-required="true" data-type="password" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">Role<span class="text-danger">*</span></label>
                                    <div class="col-sm-9">
                                        <select id="role" name="Role" class="form-control" data-required="true">
                                            <option value="">Select Role</option>
                                            <option value="admin">Admin</option>
                                            <option value="user">User</option>
                                            <option value="manager">Manager</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label">
                                        <input type="checkbox" id="terms" name="Terms" data-required="true" />
                                        Accept Terms<span class="text-danger">*</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12 text-end">
                                <button type="submit" class="btn btn-primary">Submit (Always Enabled)</button>
                                <button type="reset" class="btn btn-light">Reset</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/formValidation.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize validation for required fields
            attachRequiredValidation('#firstName', 'First Name is required');
            attachRequiredValidation('#lastName', 'Last Name is required');
            attachRequiredValidation('#email', 'Email is required', 'email');
            attachRequiredValidation('#password', 'Password is required', 'password');
            attachRequiredValidation('#role', 'Role is required');
            attachRequiredValidation('#terms', 'You must accept the terms');
            
            // Form submission handler
            $('#testForm').on('submit', function(e) {
                e.preventDefault();
                
                // The global validation handler will take care of validation
                // If we reach here, validation passed
                alert('Form submitted successfully! All validation passed.');
                
                // Here you would normally send the data to the server
                console.log('Form data:', $(this).serialize());
            });
        });
    </script>
}
